%% Fetal Y Chromosome Concentration Analysis
% Analysis of the relationship between fetal Y chromosome concentration 
% and maternal gestational weeks, BMI and other indicators

clear; clc; close all;

%% Data Loading and Preprocessing
% Read the Excel data file
data = readtable('999.xlsx');

% Display basic information about the dataset
fprintf('Dataset Information:\n');
fprintf('Number of samples: %d\n', height(data));
fprintf('Number of variables: %d\n', width(data));

% Extract relevant variables using column indices to avoid encoding issues
% Column mapping based on Excel file structure:
% Column 2: Age (年龄)
% Column 3: Height (身高)
% Column 4: Weight (体重)
% Column 5: Gestational days (检测孕天数)
% Column 6: Maternal BMI (孕妇BMI)
% Column 17: Y chromosome concentration (Y染色体浓度)

gestational_days = data{:, 5};  % Gestational days in days
gestational_weeks = gestational_days / 7;  % Convert to weeks
maternal_bmi = data{:, 6};  % Maternal BMI
y_concentration = data{:, 17};  % Y chromosome concentration
maternal_age = data{:, 2};  % Maternal age in years
maternal_height = data{:, 3};  % Maternal height in cm
maternal_weight = data{:, 4};  % Maternal weight in kg

% Remove missing values
valid_indices = ~isnan(gestational_weeks) & ~isnan(maternal_bmi) & ~isnan(y_concentration);
gestational_weeks_clean = gestational_weeks(valid_indices);
maternal_bmi_clean = maternal_bmi(valid_indices);
y_concentration_clean = y_concentration(valid_indices);
maternal_age_clean = maternal_age(valid_indices);
maternal_height_clean = maternal_height(valid_indices);
maternal_weight_clean = maternal_weight(valid_indices);

fprintf('\nAfter removing missing values:\n');
fprintf('Valid samples for analysis: %d\n', sum(valid_indices));

%% Descriptive Statistics
fprintf('\n=== Descriptive Statistics ===\n');
fprintf('Gestational weeks: Mean=%.2f, Std=%.2f, Range=[%.2f, %.2f]\n', ...
    mean(gestational_weeks_clean), std(gestational_weeks_clean), ...
    min(gestational_weeks_clean), max(gestational_weeks_clean));
fprintf('Maternal BMI: Mean=%.2f, Std=%.2f, Range=[%.2f, %.2f]\n', ...
    mean(maternal_bmi_clean), std(maternal_bmi_clean), ...
    min(maternal_bmi_clean), max(maternal_bmi_clean));
fprintf('Y chromosome concentration: Mean=%.4f, Std=%.4f, Range=[%.4f, %.4f]\n', ...
    mean(y_concentration_clean), std(y_concentration_clean), ...
    min(y_concentration_clean), max(y_concentration_clean));

%% Log Transformation of Y Chromosome Concentration
% Apply log transformation to Y chromosome concentration
log_y_concentration = log(y_concentration_clean);

fprintf('\nAfter log transformation:\n');
fprintf('Log Y concentration: Mean=%.4f, Std=%.4f, Range=[%.4f, %.4f]\n', ...
    mean(log_y_concentration), std(log_y_concentration), ...
    min(log_y_concentration), max(log_y_concentration));

%% Correlation Analysis
fprintf('\n=== Correlation Analysis ===\n');

% Calculate correlation coefficients
corr_weeks = corr(gestational_weeks_clean, log_y_concentration, 'rows', 'complete');
corr_bmi = corr(maternal_bmi_clean, log_y_concentration, 'rows', 'complete');
corr_age = corr(maternal_age_clean, log_y_concentration, 'rows', 'complete');

fprintf('Correlation between gestational weeks and log Y concentration: r = %.4f\n', corr_weeks);
fprintf('Correlation between maternal BMI and log Y concentration: r = %.4f\n', corr_bmi);
fprintf('Correlation between maternal age and log Y concentration: r = %.4f\n', corr_age);

%% Multiple Linear Regression Analysis
fprintf('\n=== Multiple Linear Regression Analysis ===\n');

% Prepare design matrix
X = [ones(length(gestational_weeks_clean), 1), gestational_weeks_clean, maternal_bmi_clean, maternal_age_clean];
y = log_y_concentration;

% Perform multiple linear regression
[beta, bint, residuals, rint, stats] = regress(y, X);

% Extract regression statistics
R_squared = stats(1);
F_statistic = stats(2);
p_value = stats(3);
error_variance = stats(4);

% Display regression results
fprintf('\nMultiple Linear Regression Results:\n');
fprintf('Equation: log(Y_concentration) = β₀ + β₁×gestational_weeks + β₂×BMI + β₃×age\n\n');
fprintf('Regression Coefficients:\n');
fprintf('Intercept (β₀): %.6f [%.6f, %.6f]\n', beta(1), bint(1,1), bint(1,2));
fprintf('Gestational weeks (β₁): %.6f [%.6f, %.6f]\n', beta(2), bint(2,1), bint(2,2));
fprintf('Maternal BMI (β₂): %.6f [%.6f, %.6f]\n', beta(3), bint(3,1), bint(3,2));
fprintf('Maternal age (β₃): %.6f [%.6f, %.6f]\n', beta(4), bint(4,1), bint(4,2));

fprintf('\nModel Statistics:\n');
fprintf('R-squared: %.4f\n', R_squared);
fprintf('F-statistic: %.4f\n', F_statistic);
fprintf('p-value: %.6f\n', p_value);
fprintf('Error variance: %.6f\n', error_variance);

%% Significance Testing
alpha = 0.05;
fprintf('\nSignificance Testing (α = %.2f):\n', alpha);
if p_value < alpha
    fprintf('The overall regression model is statistically significant (p < %.2f)\n', alpha);
else
    fprintf('The overall regression model is not statistically significant (p ≥ %.2f)\n', alpha);
end

% Individual coefficient significance
for i = 1:length(beta)
    if i == 1
        var_name = 'Intercept';
    elseif i == 2
        var_name = 'Gestational weeks';
    elseif i == 3
        var_name = 'Maternal BMI';
    else
        var_name = 'Maternal age';
    end
    
    if bint(i,1) * bint(i,2) > 0  % Confidence interval doesn't include 0
        fprintf('%s: Statistically significant\n', var_name);
    else
        fprintf('%s: Not statistically significant\n', var_name);
    end
end

%% Visualization
% Create comprehensive plots
figure('Position', [100, 100, 1200, 800]);

% Subplot 1: Scatter plot of gestational weeks vs log Y concentration
subplot(2, 3, 1);
scatter(gestational_weeks_clean, log_y_concentration, 50, 'b', 'filled', 'Alpha', 0.6);
hold on;
% Add regression line
p_weeks = polyfit(gestational_weeks_clean, log_y_concentration, 1);
x_fit = linspace(min(gestational_weeks_clean), max(gestational_weeks_clean), 100);
y_fit = polyval(p_weeks, x_fit);
plot(x_fit, y_fit, 'r-', 'LineWidth', 2);
xlabel('Gestational Weeks');
ylabel('Log Y Chromosome Concentration');
title('Gestational Weeks vs Log Y Concentration');
grid on;
legend('Data points', 'Regression line', 'Location', 'best');

% Subplot 2: Scatter plot of BMI vs log Y concentration
subplot(2, 3, 2);
scatter(maternal_bmi_clean, log_y_concentration, 50, 'g', 'filled', 'Alpha', 0.6);
hold on;
% Add regression line
p_bmi = polyfit(maternal_bmi_clean, log_y_concentration, 1);
x_fit_bmi = linspace(min(maternal_bmi_clean), max(maternal_bmi_clean), 100);
y_fit_bmi = polyval(p_bmi, x_fit_bmi);
plot(x_fit_bmi, y_fit_bmi, 'r-', 'LineWidth', 2);
xlabel('Maternal BMI');
ylabel('Log Y Chromosome Concentration');
title('Maternal BMI vs Log Y Concentration');
grid on;
legend('Data points', 'Regression line', 'Location', 'best');

% Subplot 3: Scatter plot of age vs log Y concentration
subplot(2, 3, 3);
scatter(maternal_age_clean, log_y_concentration, 50, 'm', 'filled', 'Alpha', 0.6);
hold on;
% Add regression line
p_age = polyfit(maternal_age_clean, log_y_concentration, 1);
x_fit_age = linspace(min(maternal_age_clean), max(maternal_age_clean), 100);
y_fit_age = polyval(p_age, x_fit_age);
plot(x_fit_age, y_fit_age, 'r-', 'LineWidth', 2);
xlabel('Maternal Age');
ylabel('Log Y Chromosome Concentration');
title('Maternal Age vs Log Y Concentration');
grid on;
legend('Data points', 'Regression line', 'Location', 'best');

% Subplot 4: Histogram of log Y concentration
subplot(2, 3, 4);
histogram(log_y_concentration, 20, 'FaceColor', 'skyblue', 'EdgeColor', 'black', 'Alpha', 0.7);
xlabel('Log Y Chromosome Concentration');
ylabel('Frequency');
title('Distribution of Log Y Concentration');
grid on;

% Subplot 5: Predicted vs Actual values
subplot(2, 3, 5);
y_predicted = X * beta;
scatter(y_predicted, log_y_concentration, 50, 'c', 'filled', 'Alpha', 0.6);
hold on;
% Add perfect prediction line
min_val = min([y_predicted; log_y_concentration]);
max_val = max([y_predicted; log_y_concentration]);
plot([min_val, max_val], [min_val, max_val], 'r--', 'LineWidth', 2);
xlabel('Predicted Log Y Concentration');
ylabel('Actual Log Y Concentration');
title('Predicted vs Actual Values');
grid on;
legend('Data points', 'Perfect prediction', 'Location', 'best');

% Subplot 6: Residuals vs Fitted values
subplot(2, 3, 6);
scatter(y_predicted, residuals, 50, 'orange', 'filled', 'Alpha', 0.6);
hold on;
plot([min(y_predicted), max(y_predicted)], [0, 0], 'r--', 'LineWidth', 2);
xlabel('Fitted Values');
ylabel('Residuals');
title('Residuals vs Fitted Values');
grid on;
legend('Residuals', 'Zero line', 'Location', 'best');

% Adjust subplot spacing
sgtitle('Fetal Y Chromosome Concentration Analysis', 'FontSize', 16, 'FontWeight', 'bold');

%% Residual Analysis
fprintf('\n=== Residual Analysis ===\n');

% Calculate standardized residuals
standardized_residuals = residuals / sqrt(error_variance);

% Normality test (Shapiro-Wilk approximation)
[h_normality, p_normality] = lillietest(standardized_residuals);
fprintf('Lilliefors normality test:\n');
fprintf('H0: Residuals are normally distributed\n');
fprintf('p-value: %.6f\n', p_normality);
if h_normality == 0
    fprintf('Result: Fail to reject H0 - Residuals appear normally distributed\n');
else
    fprintf('Result: Reject H0 - Residuals do not appear normally distributed\n');
end

% Durbin-Watson test for autocorrelation
dw_stat = sum(diff(residuals).^2) / sum(residuals.^2);
fprintf('\nDurbin-Watson statistic: %.4f\n', dw_stat);
fprintf('(Values close to 2 indicate no autocorrelation)\n');

% Outlier detection
outlier_threshold = 2.5;
outliers = abs(standardized_residuals) > outlier_threshold;
num_outliers = sum(outliers);
fprintf('\nOutlier Analysis:\n');
fprintf('Number of potential outliers (|standardized residual| > %.1f): %d\n', outlier_threshold, num_outliers);
if num_outliers > 0
    fprintf('Outlier indices: ');
    fprintf('%d ', find(outliers));
    fprintf('\n');
end

%% Additional Residual Plots
figure('Position', [150, 150, 1000, 600]);

% Q-Q plot for normality assessment
subplot(2, 2, 1);
qqplot(standardized_residuals);
title('Q-Q Plot of Standardized Residuals');
xlabel('Theoretical Quantiles');
ylabel('Sample Quantiles');
grid on;

% Histogram of standardized residuals
subplot(2, 2, 2);
histogram(standardized_residuals, 15, 'FaceColor', 'lightblue', 'EdgeColor', 'black', 'Alpha', 0.7);
hold on;
% Overlay normal distribution
x_norm = linspace(min(standardized_residuals), max(standardized_residuals), 100);
y_norm = normpdf(x_norm, 0, 1) * length(standardized_residuals) * (max(standardized_residuals) - min(standardized_residuals)) / 15;
plot(x_norm, y_norm, 'r-', 'LineWidth', 2);
xlabel('Standardized Residuals');
ylabel('Frequency');
title('Distribution of Standardized Residuals');
legend('Residuals', 'Normal Distribution', 'Location', 'best');
grid on;

% Residuals vs each predictor
subplot(2, 2, 3);
scatter(gestational_weeks_clean, standardized_residuals, 50, 'b', 'filled', 'Alpha', 0.6);
hold on;
plot([min(gestational_weeks_clean), max(gestational_weeks_clean)], [0, 0], 'r--', 'LineWidth', 2);
xlabel('Gestational Weeks');
ylabel('Standardized Residuals');
title('Residuals vs Gestational Weeks');
grid on;

subplot(2, 2, 4);
scatter(maternal_bmi_clean, standardized_residuals, 50, 'g', 'filled', 'Alpha', 0.6);
hold on;
plot([min(maternal_bmi_clean), max(maternal_bmi_clean)], [0, 0], 'r--', 'LineWidth', 2);
xlabel('Maternal BMI');
ylabel('Standardized Residuals');
title('Residuals vs Maternal BMI');
grid on;

sgtitle('Residual Analysis Plots', 'FontSize', 14, 'FontWeight', 'bold');

%% Model Summary and Interpretation
fprintf('\n=== MODEL SUMMARY AND INTERPRETATION ===\n');
fprintf('Final Regression Equation:\n');
fprintf('log(Y_concentration) = %.6f + %.6f×gestational_weeks + %.6f×BMI + %.6f×age\n\n', ...
    beta(1), beta(2), beta(3), beta(4));

fprintf('Key Findings:\n');
fprintf('1. Model Fit: R² = %.4f (%.1f%% of variance explained)\n', R_squared, R_squared*100);
fprintf('2. Overall Model Significance: F(3,%d) = %.4f, p = %.6f\n', ...
    length(y)-4, F_statistic, p_value);

fprintf('\n3. Individual Predictor Effects:\n');
if abs(beta(2)) > 0.001
    fprintf('   - Gestational weeks: %.1f%% change in Y concentration per week\n', (exp(beta(2))-1)*100);
end
if abs(beta(3)) > 0.001
    fprintf('   - Maternal BMI: %.1f%% change in Y concentration per BMI unit\n', (exp(beta(3))-1)*100);
end
if abs(beta(4)) > 0.001
    fprintf('   - Maternal age: %.1f%% change in Y concentration per year\n', (exp(beta(4))-1)*100);
end

fprintf('\n4. Model Assumptions:\n');
if h_normality == 0
    fprintf('   ✓ Normality: Residuals appear normally distributed\n');
else
    fprintf('   ✗ Normality: Residuals may not be normally distributed\n');
end

if dw_stat >= 1.5 && dw_stat <= 2.5
    fprintf('   ✓ Independence: No strong evidence of autocorrelation\n');
else
    fprintf('   ? Independence: Potential autocorrelation detected\n');
end

fprintf('   - Outliers: %d potential outliers detected (%.1f%% of data)\n', ...
    num_outliers, (num_outliers/length(y))*100);

fprintf('\n=== ANALYSIS COMPLETE ===\n');
fprintf('Results saved in workspace variables:\n');
fprintf('- beta: regression coefficients\n');
fprintf('- bint: confidence intervals for coefficients\n');
fprintf('- residuals: model residuals\n');
fprintf('- stats: [R², F-statistic, p-value, error variance]\n');
