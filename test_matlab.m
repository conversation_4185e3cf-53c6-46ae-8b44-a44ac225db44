%% Simple test to verify MATLAB functionality
% This is a simplified version to test core functionality

clear; clc;

% Test data loading
try
    data = readtable('999.xlsx');
    fprintf('✓ Data loading successful\n');
    fprintf('Dataset size: %d rows, %d columns\n', height(data), width(data));
catch ME
    fprintf('✗ Data loading failed: %s\n', ME.message);
    return;
end

% Test variable extraction
try
    gestational_days = data.("检测孕天数");
    gestational_weeks = gestational_days / 7;
    maternal_bmi = data.("孕妇BMI");
    y_concentration = data.("Y染色体浓度");
    maternal_age = data.("年龄");
    fprintf('✓ Variable extraction successful\n');
catch ME
    fprintf('✗ Variable extraction failed: %s\n', ME.message);
    return;
end

% Test data cleaning
try
    valid_indices = ~isnan(gestational_weeks) & ~isnan(maternal_bmi) & ~isnan(y_concentration) & ~isnan(maternal_age);
    gestational_weeks_clean = gestational_weeks(valid_indices);
    maternal_bmi_clean = maternal_bmi(valid_indices);
    y_concentration_clean = y_concentration(valid_indices);
    maternal_age_clean = maternal_age(valid_indices);
    fprintf('✓ Data cleaning successful\n');
    fprintf('Valid samples: %d\n', sum(valid_indices));
catch ME
    fprintf('✗ Data cleaning failed: %s\n', ME.message);
    return;
end

% Test log transformation
try
    log_y_concentration = log(y_concentration_clean);
    fprintf('✓ Log transformation successful\n');
catch ME
    fprintf('✗ Log transformation failed: %s\n', ME.message);
    return;
end

% Test regression
try
    X = [ones(length(gestational_weeks_clean), 1), gestational_weeks_clean, maternal_bmi_clean, maternal_age_clean];
    y = log_y_concentration;
    [beta, bint, residuals, rint, stats] = regress(y, X);
    fprintf('✓ Regression analysis successful\n');
    
    % Display key results
    fprintf('\nRegression Results:\n');
    fprintf('Intercept: %.6f\n', beta(1));
    fprintf('Gestational weeks coefficient: %.6f\n', beta(2));
    fprintf('BMI coefficient: %.6f\n', beta(3));
    fprintf('Age coefficient: %.6f\n', beta(4));
    fprintf('R-squared: %.4f\n', stats(1));
    fprintf('F-statistic: %.4f\n', stats(2));
    fprintf('p-value: %.6f\n', stats(3));
    
catch ME
    fprintf('✗ Regression analysis failed: %s\n', ME.message);
    return;
end

fprintf('\n✓ All tests passed! The main analysis code should work correctly.\n');
